@import "tailwindcss";

/* Add any custom base styles below this line */
@layer base {
  /* Set dark mode to use class instead of media query for theme toggle */
  :root {
    color-scheme: light;
  }

  .dark {
    color-scheme: dark;
  }
}

@layer utilities {
  .animate-spin-slow {
    animation: spin 20s linear infinite;
  }

  .animate-pulse {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-fade-in {
    animation: fade-in 0.2s ease-in-out;
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.2s ease-out;
  }

  /* Smooth text expansion animation */
  .description-expand {
    transition: all 0.3s ease-in-out;
  }

  .description-expand.expanded {
    animation: text-expand 0.3s ease-out;
  }

  /* Smooth tags expansion animation */
  .tags-container {
    transition: all 0.2s ease-in-out;
  }

  .tag-item {
    transition: all 0.2s ease-in-out;
    animation: tag-fade-in 0.2s ease-out;
  }

  .tag-item:hover {
    transform: translateY(-1px);
  }

  /* Text truncation utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced Tooltip animations */
  .tooltip-enter {
    animation: tooltip-slide-in 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .tooltip-exit {
    animation: tooltip-slide-out 0.15s cubic-bezier(0.4, 0, 1, 1);
  }

  /* Tooltip content animations */
  .tooltip-content-enter {
    animation: tooltip-content-reveal 0.25s cubic-bezier(0.16, 1, 0.3, 1) 0.05s
      both;
  }

  /* Tooltip arrow animations */
  .tooltip-arrow {
    animation: tooltip-arrow-appear 0.2s cubic-bezier(0.16, 1, 0.3, 1) 0.1s both;
  }

  /* Enhanced tooltip default styling */
  .enhanced-tooltip-default {
    position: relative;
  }

  .enhanced-tooltip-default::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
    z-index: 1;
  }

  /* Professional hover effects for interactive tooltips */
  .tooltip-interactive:hover {
    transform: translateY(-1px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Smooth transitions for all tooltip elements */
  .tooltip-transition {
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Subtle task card highlight for dependency navigation */
  .dependency-highlight {
    border-left: 4px solid rgb(59 130 246) !important;
    background: rgba(59, 130, 246, 0.02) !important;
    transition: all 0.3s ease-in-out !important;
  }

  .dark .dependency-highlight {
    background: rgba(59, 130, 246, 0.05) !important;
  }

  /* Professional task title highlighting */
  .task-title-highlighted {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.1),
      rgba(99, 102, 241, 0.08)
    ) !important;
    color: rgb(29 78 216) !important;
    font-weight: 600 !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border-left: 3px solid rgb(59 130 246) !important;
    transition: all 0.3s ease-in-out !important;
    position: relative !important;
  }

  .dark .task-title-highlighted {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.15),
      rgba(99, 102, 241, 0.12)
    ) !important;
    color: rgb(147 197 253) !important;
  }

  /* Subtle navigation indicator for task titles */
  .task-title-navigation-indicator::before {
    content: "→ ";
    color: rgb(59 130 246);
    font-weight: bold;
    margin-right: 4px;
  }

  .dark .task-title-navigation-indicator::before {
    color: rgb(147 197 253);
  }

  /* Modern Dashboard Animations */
  .animate-scale-in {
    animation: scale-in 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-slide-up {
    animation: slide-up 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Hero section gradient text animation */
  .gradient-text {
    background-size: 200% auto;
    animation: gradient-text-flow 3s linear infinite;
  }

  /* Card hover glow effect */
  .card-glow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(59, 130, 246, 0.1),
      0 0 20px rgba(59, 130, 246, 0.1);
  }

  .dark .card-glow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.2),
      0 0 20px rgba(59, 130, 246, 0.15);
  }

  /* Progress ring animation */
  .progress-ring {
    transition: stroke-dashoffset 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Floating action button */
  .floating-action {
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .floating-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Professional compact design utilities */
  .compact-card {
    @apply bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200;
  }

  .compact-header {
    @apply flex items-center space-x-2 mb-3;
  }

  .compact-icon {
    @apply w-6 h-6 rounded-md flex items-center justify-center shadow-sm;
  }

  .compact-title {
    @apply text-sm font-semibold text-gray-900 dark:text-gray-100;
  }

  .compact-subtitle {
    @apply text-xs text-gray-600 dark:text-gray-400;
  }

  .compact-button {
    @apply inline-flex items-center px-3 py-1.5 text-xs rounded-lg transition-all duration-200;
  }

  .compact-stats {
    @apply flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 px-3 py-1.5 rounded-lg border border-gray-200 dark:border-gray-700;
  }

  .compact-indicator {
    @apply w-1.5 h-1.5 rounded-full;
  }

  /* Enhanced professional animations */
  .professional-hover {
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .professional-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .dark .professional-hover:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* Subtle glass effect for modern UI */
  .glass-effect {
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background: rgba(31, 41, 55, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.2);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(-4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateX(-8px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes text-expand {
  0% {
    opacity: 0.7;
    transform: translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tag-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Tooltip Keyframes */
@keyframes tooltip-slide-in {
  from {
    opacity: 0;
    transform: scale(0.92) translateY(8px);
    filter: blur(4px);
  }
  50% {
    opacity: 0.8;
    filter: blur(1px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

@keyframes tooltip-slide-out {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(4px);
    filter: blur(2px);
  }
}

@keyframes tooltip-content-reveal {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tooltip-arrow-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Legacy animations for backward compatibility */
@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes tooltip-fade-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* Modern Dashboard Keyframes */
@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-text-flow {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}
